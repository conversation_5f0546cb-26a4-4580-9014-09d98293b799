const TRENDING_NOW_URL="https://www.etsy.com/market/trending_now",UNINSTALL_REDIRECT_URL="https://everbee.io/extension-uninstall",INSTALL_REDIRECT_URL="https://auth.everbee.io/beta-getting-started?redirect_to=https://etsy.com",UPDATE_REDIRECT_URL="https://stag-auth.everbee.io/beta-getting-started",CONTACT_US_URL="https://everbee.io/contact-us";function storageSet({key:e,value:t}){return new Promise((o=>{chrome.storage?.local.set({[e]:t},(function(){o()}))}))}function storageGet(e){return new Promise((t=>{chrome.storage?.local.get([e],(function(o){t(o[e])}))}))}async function checkIsPinned(){let e=await chrome.action.getUserSettings();await storageSet({key:"extInfo",value:e})}chrome.runtime.onInstalled.addListener((async function(e){chrome.action.setBadgeText({text:"ON"}),chrome.action.setBadgeBackgroundColor({color:"rgb(32, 179, 32)"}),await storageSet({key:"activeExt",value:!0}),"install"===e.reason?chrome.tabs.create({url:INSTALL_REDIRECT_URL}):"update"===e.reason&&setTimeout((function(){chrome.tabs.query({url:"https://www.etsy.com/*"},(function(e){e.forEach((function(e,t){chrome.tabs.reload(e.id)}))}))}),500)})),chrome.runtime.setUninstallURL(UNINSTALL_REDIRECT_URL),chrome.action.onClicked.addListener((async function(e){await storageGet("activeExt")?(chrome.action.setBadgeText({text:"OFF"}),chrome.action.setBadgeBackgroundColor({color:"#f00"}),await storageSet({key:"activeExt",value:!1})):(chrome.action.setBadgeText({text:"ON"}),chrome.action.setBadgeBackgroundColor({color:"rgb(32, 179, 32)"}),await storageSet({key:"activeExt",value:!0})),setTimeout((function(){chrome.tabs.query({url:"https://www.etsy.com/*"},(function(e){e.forEach((function(e,t){chrome.tabs.reload(e.id)}))}))}),500)})),chrome.tabs.onUpdated.addListener((function(e,t,o){"complete"!=t.status&&"loading"!=t.status||checkIsPinned()})),chrome.runtime.onMessageExternal.addListener(((e,t,o)=>(console.log(e),e.removeToken?chrome.storage.local.remove("everbeeToken",(()=>{console.log("Token removed")})):e.removeStagToken?chrome.storage.local.remove("everbeeStagToken",(()=>{console.log("Token removed"),o({success:!0})})):e.everbeeToken?chrome.storage.local.set({everbeeToken:e.everbeeToken},(()=>{console.log("Token stored in extension"),o({success:!0})})):e.everbeeStagToken?chrome.storage.local.set({everbeeStagToken:e.everbeeStagToken},(()=>{console.log("Token stored in extension"),o({success:!0})})):o({success:!1,error:"No message received"}),!0)));